#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试VirtualLogView文本选择功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from PyQt5.QtWidgets import <PERSON>A<PERSON><PERSON>, QMainWindow, QVBoxLayout, QWidget, QPushButton, QHBoxLayout
    from PyQt5.QtCore import QTimer
    from views.virtual_log_view import VirtualLogView
    print("所有模块导入成功")
except Exception as e:
    print(f"导入模块时出错: {e}")
    sys.exit(1)

class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("VirtualLogView 文本选择功能测试")
        self.setGeometry(100, 100, 800, 600)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        
        # 创建按钮布局
        button_layout = QHBoxLayout()
        
        # 添加测试日志按钮
        add_log_btn = QPushButton("添加测试日志")
        add_log_btn.clicked.connect(self.add_test_logs)
        button_layout.addWidget(add_log_btn)
        
        # 清空日志按钮
        clear_btn = QPushButton("清空日志")
        clear_btn.clicked.connect(self.clear_logs)
        button_layout.addWidget(clear_btn)
        
        # 获取选中文本按钮
        get_selection_btn = QPushButton("获取选中文本")
        get_selection_btn.clicked.connect(self.get_selected_text)
        button_layout.addWidget(get_selection_btn)
        
        layout.addLayout(button_layout)
        
        # 创建虚拟日志视图
        self.log_view = VirtualLogView()
        layout.addWidget(self.log_view)
        
        # 添加初始测试数据
        self.add_test_logs()
        
    def add_test_logs(self):
        """添加测试日志"""
        test_logs = [
            "🔨 开始编译阶段...",
            "检查编译环境...",
            "  检查VCS许可证... ✓",
            "  检查SystemVerilog编译器... ✓",
            "  检查UVM库... ✓",
            "  检查覆盖率工具... ✓",
            "  检查波形工具... ✓",
            "",
            "[编译步骤 1/6] 解析设计文件...",
            "  解析文件 1/5: top.sv",
            "    模块: top_module",
            "    端口: 32个输入, 16个输出",
            "    实例: cpu_core, memory_ctrl, bus_fabric",
            "  解析文件 2/5: cpu_core.sv",
            "    模块: cpu_core",
            "    端口: 64个输入, 32个输出",
            "    特性: 5级流水线, 分支预测, 缓存控制",
            "  解析文件 3/5: memory_ctrl.sv",
            "    模块: memory_controller",
            "    端口: 48个输入, 24个输出",
            "    支持: DDR4, LPDDR4, 错误检测与纠正",
            "",
            "🚀 开始仿真阶段...",
            "初始化仿真环境...",
            "  设置随机种子: 12345",
            "  配置UVM环境",
            "  启动时钟生成器",
            "  复位系统...",
            "",
            "=== 仿真周期 1 ===",
            "时间: 0ns, 时钟: 上升沿",
            "CPU状态: 复位中",
            "内存状态: 初始化",
            "总线状态: 空闲",
            "",
            "=== 仿真周期 10 ===",
            "时间: 100ns, 时钟: 上升沿",
            "CPU状态: 取指令",
            "指令地址: 0x00000000",
            "指令内容: ADD R1, R2, R3",
            "",
            "✅ 仿真完成",
            "总仿真时间: 1000ns",
            "执行指令数: 50",
            "覆盖率: 85.6%",
            "测试结果: PASS"
        ]
        
        for log in test_logs:
            self.log_view.append(log + "\n")
    
    def clear_logs(self):
        """清空日志"""
        self.log_view.clear()
    
    def get_selected_text(self):
        """获取选中的文本并打印"""
        selected_text = self.log_view._get_selected_text()
        if selected_text:
            print("选中的文本:")
            print("=" * 50)
            print(selected_text)
            print("=" * 50)
        else:
            print("没有选中任何文本")

def main():
    app = QApplication(sys.argv)
    
    # 设置应用程序属性
    app.setApplicationName("VirtualLogView 文本选择测试")
    app.setApplicationVersion("1.0")
    
    # 创建主窗口
    window = TestWindow()
    window.show()
    
    print("VirtualLogView 文本选择功能测试")
    print("=" * 50)
    print("测试说明:")
    print("1. 单击并拖拽可以选择文本")
    print("2. 双击可以选择单词")
    print("3. 三击可以选择整行")
    print("4. Ctrl+C 可以复制选中的文本")
    print("5. Ctrl+A 可以全选")
    print("6. 右键菜单提供复制、全选、清除选择功能")
    print("7. ESC 键可以清除选择")
    print("8. 点击'获取选中文本'按钮可以在控制台查看选中内容")
    print("=" * 50)
    
    # 运行应用程序
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
